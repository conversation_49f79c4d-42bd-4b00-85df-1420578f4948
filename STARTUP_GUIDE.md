# Chrome扩展开发环境启动指南

## 问题解决

### 原始问题
1. **Vite服务器重复监听错误**: `Error [ERR_SERVER_ALREADY_LISTEN]: Listen method has been called more than once without closing.`
2. **Chrome命令找不到**: `/bin/sh: google-chrome: command not found`

### 解决方案

#### 1. 修复Vite配置
- 在 `vite.config.ts` 中添加了 `strictPort: false` 和 `host: true`
- 允许Vite自动选择可用端口

#### 2. 修复Chrome启动命令
- 更新了Chrome命令优先级，macOS路径优先
- 添加了Chrome应用存在性检查
- 改进了错误处理

#### 3. 创建简化启动脚本
- 创建了 `scripts/simple-start.js` 避免Vite服务器冲突
- 添加了新的npm脚本 `start:simple`

## 使用方法

### 方法1: 简化启动（推荐）
```bash
npm run start:simple
```

这个命令会：
- 检查并构建扩展
- 打开Chrome浏览器并加载扩展
- 避免Vite服务器冲突

### 方法2: 完整开发环境
```bash
npm run start
```

这个命令会：
- 启动Vite开发服务器
- 打开Chrome浏览器并加载扩展
- 支持热重载

### 方法3: 仅开发模式
```bash
npm run dev:extension
```

这个命令会：
- 启动文件监听器
- 自动重新构建扩展
- 需要手动在Chrome中加载扩展

## 开发流程

1. **启动开发环境**: `npm run start:simple`
2. **修改代码**: 编辑 `src/` 目录下的文件
3. **重新构建**: `npm run build` (如果使用简化启动)
4. **重新加载扩展**: 在Chrome扩展管理页面点击刷新按钮

## 故障排除

### 如果Chrome无法启动
- 确保Chrome浏览器已安装
- 检查Chrome是否已经在运行
- 手动打开Chrome并访问 `chrome://extensions/`
- 开启"开发者模式"
- 点击"加载已解压的扩展程序"
- 选择 `dist` 目录

### 如果端口冲突
```bash
# 杀死占用端口的进程
lsof -ti:3000,3001,3002 | xargs kill -9 2>/dev/null || true
```

### 如果扩展无法加载
- 检查 `dist/manifest.json` 是否存在
- 运行 `npm run build` 重新构建
- 在Chrome扩展管理页面重新加载扩展

## 文件结构

```
scripts/
├── start.js          # 完整启动脚本（包含Vite服务器）
├── simple-start.js   # 简化启动脚本（推荐）
├── dev.js           # 开发模式脚本
└── test-build.js    # 测试构建脚本
```

## 注意事项

- 使用 `start:simple` 时，修改代码后需要手动运行 `npm run build`
- 使用 `start` 时，Vite会自动处理热重载
- Chrome错误信息 `Failed to create SingletonLock` 是正常的，表示Chrome已经在运行

