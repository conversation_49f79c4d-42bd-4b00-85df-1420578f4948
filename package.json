{"name": "chrome-extension-vite", "version": "1.0.0", "description": "Chrome Extension with Vite, React, TypeScript", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "start": "node scripts/start.js", "start:simple": "node scripts/simple-start.js", "dev:extension": "node scripts/dev.js", "test:build": "node scripts/test-build.js"}, "dependencies": {"@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-slider": "^1.3.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-toggle": "^1.1.10", "@tailwindcss/vite": "^4.1.13", "cash-dom": "^8.1.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.294.0", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwind-merge": "^3.3.1", "zod": "^3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.0", "@types/chrome": "^0.0.251", "@types/node": "^24.5.2", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "chokidar": "^3.5.3", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.32", "tailwindcss": "^4.1.13", "tw-animate-css": "^1.4.0", "typescript": "^5.2.2", "vite": "^5.0.8"}}