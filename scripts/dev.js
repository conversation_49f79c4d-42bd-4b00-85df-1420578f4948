#!/usr/bin/env node

import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs';
import { watch } from 'chokidar';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, '..');
const distPath = join(projectRoot, 'dist');

// 构建扩展
async function buildExtension() {
  console.log('🔨 构建扩展...');
  
  return new Promise((resolve, reject) => {
    const buildProcess = spawn('npm', ['run', 'build'], {
      cwd: projectRoot,
      stdio: 'inherit',
      shell: true
    });

    buildProcess.on('close', (code) => {
      if (code === 0) {
        console.log('✅ 扩展构建完成');
        resolve();
      } else {
        reject(new Error(`构建失败，退出码: ${code}`));
      }
    });
  });
}

// 监听文件变化并重新构建
function startWatcher() {
  console.log('🔥 启动文件监听器...');
  
  const watcher = watch('src/**/*', {
    cwd: projectRoot,
    ignored: /node_modules/,
    persistent: true
  });

  let isBuilding = false;

  watcher.on('change', async (path) => {
    if (isBuilding) return;
    
    console.log(`📝 文件变化: ${path}`);
    isBuilding = true;
    
    try {
      await buildExtension();
      console.log('🔄 扩展已重新构建，请在Chrome中重新加载扩展');
    } catch (error) {
      console.error('❌ 构建失败:', error.message);
    } finally {
      isBuilding = false;
    }
  });

  watcher.on('add', (path) => {
    console.log(`➕ 新文件: ${path}`);
  });

  watcher.on('unlink', (path) => {
    console.log(`🗑️ 删除文件: ${path}`);
  });

  return watcher;
}

// 主函数
async function main() {
  try {
    console.log('🚀 启动Chrome扩展开发模式...\n');

    // 初始构建
    await buildExtension();

    // 启动文件监听器
    const watcher = startWatcher();

    console.log('\n🎉 开发模式启动完成！');
    console.log('📝 提示:');
    console.log('  1. 手动打开Chrome浏览器');
    console.log('  2. 访问 chrome://extensions/');
    console.log('  3. 开启"开发者模式"');
    console.log('  4. 点击"加载已解压的扩展程序"');
    console.log(`  5. 选择目录: ${distPath}`);
    console.log('  6. 修改代码后会自动重新构建');
    console.log('  7. 在Chrome中点击扩展的刷新按钮重新加载');
    console.log('  8. 按 Ctrl+C 停止开发模式');

    // 处理退出信号
    process.on('SIGINT', () => {
      console.log('\n🛑 正在停止开发模式...');
      watcher.close();
      process.exit(0);
    });

  } catch (error) {
    console.error('❌ 启动失败:', error.message);
    process.exit(1);
  }
}

// 运行主函数
main();
