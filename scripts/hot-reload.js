#!/usr/bin/env node

import { watch } from 'chokidar';
import { join } from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, '..');
const distPath = join(projectRoot, 'dist');

// 监听文件变化
const watcher = watch('src/**/*', {
  cwd: projectRoot,
  ignored: /node_modules/,
  persistent: true
});

console.log('🔥 热更新监听器已启动...');

watcher.on('change', (path) => {
  console.log(`📝 文件变化: ${path}`);
  console.log('🔄 重新构建扩展...');
  
  // 这里可以添加自动重新构建的逻辑
  // 或者发送消息给Chrome扩展重新加载
});

watcher.on('add', (path) => {
  console.log(`➕ 新文件: ${path}`);
});

watcher.on('unlink', (path) => {
  console.log(`🗑️ 删除文件: ${path}`);
});

// 处理退出信号
process.on('SIGINT', () => {
  console.log('\n🛑 停止热更新监听器...');
  watcher.close();
  process.exit(0);
});

console.log('✅ 热更新监听器运行中，按 Ctrl+C 停止');
