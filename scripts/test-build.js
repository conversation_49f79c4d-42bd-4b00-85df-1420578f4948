#!/usr/bin/env node

import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, '..');
const distPath = join(projectRoot, 'dist');

// 检查扩展是否已构建
function checkExtensionBuilt() {
  const manifestPath = join(distPath, 'manifest.json');
  return fs.existsSync(manifestPath);
}

// 构建扩展
async function buildExtension() {
  console.log('🔨 构建扩展...');
  
  return new Promise((resolve, reject) => {
    const buildProcess = spawn('npm', ['run', 'build'], {
      cwd: projectRoot,
      stdio: 'inherit',
      shell: true
    });

    buildProcess.on('close', (code) => {
      if (code === 0) {
        console.log('✅ 扩展构建完成');
        resolve();
      } else {
        reject(new Error(`构建失败，退出码: ${code}`));
      }
    });
  });
}

// 主函数
async function main() {
  try {
    console.log('🚀 测试Chrome扩展构建...\n');

    // 检查扩展是否已构建
    if (!checkExtensionBuilt()) {
      console.log('📦 扩展未构建，开始构建...');
      await buildExtension();
    } else {
      console.log('✅ 扩展已构建');
    }

    console.log('\n🎉 构建测试完成！');
    console.log('📝 下一步:');
    console.log('  1. 手动打开Chrome浏览器');
    console.log('  2. 访问 chrome://extensions/');
    console.log('  3. 开启"开发者模式"');
    console.log('  4. 点击"加载已解压的扩展程序"');
    console.log(`  5. 选择目录: ${distPath}`);
    console.log('  6. 访问任意网页并点击扩展图标测试功能');

  } catch (error) {
    console.error('❌ 构建失败:', error.message);
    process.exit(1);
  }
}

// 运行主函数
main();
