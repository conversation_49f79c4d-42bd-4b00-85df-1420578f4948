#!/usr/bin/env node

import { spawn, execSync } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, '..');

// 要打开的网页列表
const TARGET_URLS = [
  'chrome://extensions/',  // 首先打开扩展管理页面
  'https://www.google.com',
  'https://www.github.com',
  'https://developer.chrome.com/docs/extensions/',
  'https://vitejs.dev/',
  'https://react.dev/'
];

// Chrome扩展路径
const EXTENSION_PATH = join(projectRoot, 'dist');

// 检查扩展是否已构建
function checkExtensionBuilt() {
  const manifestPath = join(EXTENSION_PATH, 'manifest.json');
  return fs.existsSync(manifestPath);
}

// 构建扩展
async function buildExtension() {
  console.log('🔨 构建扩展...');
  
  return new Promise((resolve, reject) => {
    const buildProcess = spawn('npm', ['run', 'build'], {
      cwd: projectRoot,
      stdio: 'inherit',
      shell: true
    });

    buildProcess.on('close', (code) => {
      if (code === 0) {
        console.log('✅ 扩展构建完成');
        resolve();
      } else {
        reject(new Error(`构建失败，退出码: ${code}`));
      }
    });
  });
}

// 启动开发服务器
function startDevServer() {
  console.log('🚀 启动开发服务器...');
  
  const devProcess = spawn('npm', ['run', 'dev'], {
    cwd: projectRoot,
    stdio: 'inherit',
    shell: true,
    env: { 
      ...process.env, 
      PORT: '3002'  // 使用不同的端口避免冲突
    }
  });

  return devProcess;
}

// 打开Chrome浏览器并加载扩展
function openChromeWithExtension() {
  console.log('🌐 打开Chrome浏览器...');
  
  const chromeArgs = [
    '--load-extension=' + EXTENSION_PATH,
    '--disable-extensions-except=' + EXTENSION_PATH,
    '--enable-extensions',
    '--no-first-run',
    '--no-default-browser-check',
    '--disable-default-apps',
    '--disable-popup-blocking',
    '--disable-translate',
    '--disable-background-timer-throttling',
    '--disable-backgrounding-occluded-windows',
    '--disable-renderer-backgrounding',
    '--disable-features=TranslateUI',
    '--disable-ipc-flooding-protection',
    '--enable-automation',
    '--password-store=basic',
    '--use-mock-keychain',
    '--new-window',  // 在新窗口中打开
    '--user-data-dir=/tmp/chrome-extension-dev',  // 使用临时用户数据目录
    ...TARGET_URLS
  ];

  // 尝试不同的Chrome命令 (macOS优先)
  const chromeCommands = [
    '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome',
    '/Applications/Chromium.app/Contents/MacOS/Chromium',
    'google-chrome',
    'google-chrome-stable', 
    'chromium-browser',
    'chromium'
  ];
  
  let chromeProcess = null;
  let found = false;
  
  for (const cmd of chromeCommands) {
    try {
      // 检查命令是否存在
      if (cmd.startsWith('/Applications/')) {
        // 检查macOS应用是否存在
        if (fs.existsSync(cmd)) {
          chromeProcess = spawn(cmd, chromeArgs, {
            stdio: 'inherit',
            shell: false
          });
          found = true;
          console.log(`✅ 使用Chrome命令: ${cmd}`);
          break;
        }
      } else {
        // 检查系统命令是否存在
        try {
          execSync(`which ${cmd}`, { stdio: 'ignore' });
          chromeProcess = spawn(cmd, chromeArgs, {
            stdio: 'inherit',
            shell: true
          });
          found = true;
          console.log(`✅ 使用Chrome命令: ${cmd}`);
          break;
        } catch (e) {
          continue;
        }
      }
    } catch (error) {
      continue;
    }
  }
  
  if (!found) {
    console.log('❌ 未找到Chrome浏览器');
    console.log('💡 请手动打开Chrome并加载扩展:', EXTENSION_PATH);
    console.log('💡 或者安装Chrome浏览器');
    return null;
  }

  return chromeProcess;
}

// 主函数
async function main() {
  try {
    console.log('🚀 启动Chrome扩展开发环境...\n');

    // 检查扩展是否已构建
    if (!checkExtensionBuilt()) {
      console.log('📦 扩展未构建，开始构建...');
      await buildExtension();
    } else {
      console.log('✅ 扩展已构建');
    }

    // 启动开发服务器
    const devProcess = startDevServer();
    
    // 等待开发服务器启动
    console.log('⏳ 等待开发服务器启动...');
    await new Promise(resolve => setTimeout(resolve, 5000));

    // 打开Chrome浏览器
    const chromeProcess = openChromeWithExtension();

    console.log('\n🎉 开发环境启动完成！');
    console.log('📝 重要提示:');
    console.log('  1. Chrome已打开扩展管理页面: chrome://extensions/');
    console.log('  2. 开启右上角的"开发者模式"开关');
    console.log('  3. 点击"加载已解压的扩展程序"');
    console.log(`  4. 选择目录: ${EXTENSION_PATH}`);
    console.log('  5. 扩展加载后，点击扩展图标测试功能');
    console.log('  6. 修改代码后会自动重新构建');
    console.log('  7. 在扩展管理页面点击刷新按钮重新加载扩展');
    console.log('  8. 按 Ctrl+C 停止开发服务器');

    // 处理退出信号
    process.on('SIGINT', () => {
      console.log('\n🛑 正在停止开发环境...');
      if (devProcess) {
        devProcess.kill('SIGTERM');
      }
      if (chromeProcess) {
        chromeProcess.kill('SIGTERM');
      }
      process.exit(0);
    });

    // 处理未捕获的异常
    process.on('uncaughtException', (error) => {
      console.error('❌ 未捕获的异常:', error);
      if (devProcess) {
        devProcess.kill('SIGTERM');
      }
      if (chromeProcess) {
        chromeProcess.kill('SIGTERM');
      }
      process.exit(1);
    });

  } catch (error) {
    console.error('❌ 启动失败:', error.message);
    process.exit(1);
  }
}

// 运行主函数
main();
