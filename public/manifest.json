{"manifest_version": 3, "name": "Chrome Extension Vite", "version": "1.0.0", "description": "Chrome Extension built with Vite, React, TypeScript", "permissions": ["activeTab", "storage", "scripting"], "icons": {"16": "icon/16.png", "32": "icon/32.png", "48": "icon/48.png", "96": "icon/96.png", "128": "icon/128.png"}, "host_permissions": ["<all_urls>"], "background": {"service_worker": "background.js"}, "content_scripts": [{"matches": ["<all_urls>"], "js": ["content.js"]}], "action": {"default_title": "Chrome Extension Vite"}, "web_accessible_resources": [{"resources": ["content-iframe.html"], "matches": ["<all_urls>"]}]}