// Background script for Chrome Extension
console.log('Background script loaded');

// 监听扩展图标点击事件
chrome.action.onClicked.addListener(async (tab) => {
    console.log('Extension icon clicked');

    if (!tab.id) {
        console.error('No tab ID available');
        return;
    }

    // 检查tab URL是否有效
    if (!tab.url || tab.url.startsWith('chrome://') || tab.url.startsWith('chrome-extension://')) {
        console.warn('⚠️ Cannot inject content script into this page:', tab.url);
        return;
    }

    try {
        // 向content script发送消息
        await chrome.tabs.sendMessage(tab.id, {
            type: 'TOGGLE_EXTENSION',
            data: {
                timestamp: Date.now(),
            },
        });
        console.log('Message sent to content script');
    } catch (error) {
        console.error('Error sending message to content script:', error);

        // 如果content script未加载，先注入它
        try {
            await chrome.scripting.executeScript({
                target: { tabId: tab.id },
                files: ['content.js'],
            });

            // 等待一下再发送消息
            setTimeout(async () => {
                try {
                    await chrome.tabs.sendMessage(tab.id!, {
                        type: 'TOGGLE_EXTENSION',
                        data: {
                            timestamp: Date.now(),
                        },
                    });
                } catch (retryError) {
                    console.error('Retry failed:', retryError);
                }
            }, 100);
        } catch (injectError) {
            console.error('Error injecting content script:', injectError);
        }
    }
});

// 监听来自content script的消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    console.log('Message received in background:', message);

    if (message.type === 'EXTENSION_TOGGLED') {
        console.log('Extension toggled in tab:', sender.tab?.id);
    }

    sendResponse({ success: true });
});

// 扩展安装时的初始化
chrome.runtime.onInstalled.addListener((details) => {
    console.log('Extension installed/updated:', details.reason);
});
