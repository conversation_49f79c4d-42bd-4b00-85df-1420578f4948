import { useSettingsStore, settingsStoreInstance } from './setting';
import { useAppStore, appStoreInstance } from './app';

// 组合store的hook（用于React组件）
export const useStores = () => {
  const settingsStore = useSettingsStore();
  const appStore = useAppStore();

  return {
    settings: settingsStore,
    app: appStore,
  };
};

// 组合store实例（用于非React环境，如content script main函数）
export const getStoreInstances = () => {
  return {
    settings: settingsStoreInstance.getState(),
    app: appStoreInstance.getState(),
    // 提供直接的store访问方法
    settingsStore: settingsStoreInstance,
    appStore: appStoreInstance,
  };
};