import { create } from 'zustand';
import type { AppState } from '../lib/schemas';

// 应用状态store
interface AppStore extends AppState {
  setInitialized: (initialized: boolean) => void;
  setCurrentUrl: (url: string) => void;
  setUIVisible: (visible: boolean) => void;
  reset: () => void;
}

// 创建store实例
const appStore = create<AppStore>((set) => ({
  // 初始状态
  isInitialized: false,
  currentUrl: undefined,
  isUIVisible: false,

  // Actions
  setInitialized: (initialized: boolean) => {
    console.log(`🚀 App initialization status: ${initialized}`);
    set({ isInitialized: initialized });
  },

  setCurrentUrl: (url: string) => {
    console.log(`🌐 Current URL updated: ${url}`);
    set({ currentUrl: url });
  },

  setUIVisible: (visible: boolean) => {
    console.log(`current ui visible ${visible}`);
    set({ isUIVisible: visible });
  },

  reset: () => {
    console.log('🔄 Resetting app store');
    set({
      isInitialized: false,
      currentUrl: undefined,
    });
  },
}));

// 导出hook（用于React组件）
export const useAppStore = appStore;

// 导出store实例（用于非React环境）
export const appStoreInstance = appStore;