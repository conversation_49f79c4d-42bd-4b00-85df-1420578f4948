import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import type { UserSettings } from '../lib/schemas';
import { UserSettingsSchema } from '../lib/schemas';

// 用户设置store (持久化)
interface SettingsStore extends UserSettings {
  updateSettings: (settings: Partial<UserSettings>) => void;
  resetSettings: () => void;
  getSettings: () => UserSettings;
}

const defaultSettings: UserSettings = UserSettingsSchema.parse({});

// 创建settings store实例
const settingsStore = create<SettingsStore>()(
  persist(
    (set, get) => ({
      // 初始设置
      ...defaultSettings,

      // Actions
      updateSettings: (newSettings: Partial<UserSettings>) => {
        console.log('⚙️ Updating settings:', newSettings);
        const currentSettings = get();
        const updatedSettings = { ...currentSettings, ...newSettings };

        // 验证设置
        try {
          const validatedSettings = UserSettingsSchema.parse(updatedSettings);
          set(validatedSettings);
          console.log('✅ Settings updated successfully');
        } catch (error) {
          console.error('❌ Invalid settings:', error);
        }
      },

      resetSettings: () => {
        console.log('🔄 Resetting settings to defaults');
        set(defaultSettings);
      },

      getSettings: () => {
        const settings = get();
        return {
          theme: settings.theme,
          fontSize: settings.fontSize,
          fontFamily: settings.fontFamily,
          lineHeight: settings.lineHeight,
          contentWidth: settings.contentWidth,
          showImage: settings.showImage,
          showVideo: settings.showVideo
        };
      },
    }),
    {
      name: 'reader-settings',
      storage: createJSONStorage(() => {
        // 在浏览器扩展环境中使用chrome.storage.local
        if (typeof browser !== 'undefined' && browser.storage) {
          return {
            getItem: async (name: string) => {
              try {
                const result = await browser.storage.local.get(name);
                return result[name] || null;
              } catch (error) {
                console.error('❌ Failed to get item from storage:', error);
                return null;
              }
            },
            setItem: async (name: string, value: string) => {
              try {
                await browser.storage.local.set({ [name]: value });
              } catch (error) {
                console.error('❌ Failed to set item in storage:', error);
              }
            },
            removeItem: async (name: string) => {
              try {
                await browser.storage.local.remove(name);
              } catch (error) {
                console.error('❌ Failed to remove item from storage:', error);
              }
            },
          };
        }
        // 回退到localStorage
        return localStorage;
      }),
    }
  )
);

// 导出hook（用于React组件）
export const useSettingsStore = settingsStore;

// 导出store实例（用于非React环境）
export const settingsStoreInstance = settingsStore;