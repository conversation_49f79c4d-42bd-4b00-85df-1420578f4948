// Content script for Chrome Extension

console.log('Content script loaded');

// 存储iframe引用
let extensionIframe: HTMLIFrameElement | null = null;
let isExtensionVisible = false;

// 创建iframe
function createExtensionIframe(): HTMLIFrameElement {
    const iframe = document.createElement('iframe');
    iframe.id = 'chrome-extension-iframe';
    iframe.src = chrome.runtime.getURL('content-iframe.html');
    iframe.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    width: 400px;
    height: 600px;
    border: none;
    border-radius: 12px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    z-index: 2147483647;
    background: white;
    display: none;
  `;

    return iframe;
}

// 显示扩展
function showExtension() {
    if (!extensionIframe) {
        extensionIframe = createExtensionIframe();
        document.body.appendChild(extensionIframe);
    }

    extensionIframe.style.display = 'block';
    isExtensionVisible = true;

    // 添加点击外部关闭功能
    setTimeout(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (extensionIframe && !extensionIframe.contains(event.target as Node)) {
                hideExtension();
                document.removeEventListener('click', handleClickOutside);
            }
        };
        document.addEventListener('click', handleClickOutside);
    }, 100);
}

// 隐藏扩展
function hideExtension() {
    if (extensionIframe) {
        extensionIframe.style.display = 'none';
        isExtensionVisible = false;
    }
}

// 切换扩展显示状态
function toggleExtension() {
    if (isExtensionVisible) {
        hideExtension();
    } else {
        showExtension();
    }
}

// 监听来自background script的消息
chrome.runtime.onMessage.addListener((message, _sender, sendResponse) => {
    console.log('Content script received message:', message);

    if (message.type === 'TOGGLE_EXTENSION') {
        toggleExtension();

        // 向background script发送确认消息
        chrome.runtime.sendMessage({
            type: 'EXTENSION_TOGGLED',
            data: {
                visible: isExtensionVisible,
                timestamp: Date.now()
            }
        });
    }

    sendResponse({ success: true });
});

// 页面加载完成后的初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        console.log('DOM loaded, content script ready');
    });
} else {
    console.log('Content script ready');
}

// 监听页面卸载，清理资源
window.addEventListener('beforeunload', () => {
    if (extensionIframe) {
        extensionIframe.remove();
        extensionIframe = null;
    }
});
