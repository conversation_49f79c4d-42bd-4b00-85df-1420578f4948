import { cn } from '@/lib/utils';
import { useStores } from '@/entries/content/store';

export default function Article() {
    const { settings } = useStores();
    return (
        <div className="pb-20 md:pb-8 px-4">
            <div
                className="mx-auto transition-all duration-300 ease-in-out"
                style={{ maxWidth: `${settings.contentWidth}px` }}
            >
                <article
                    className={cn('prose prose-lg max-w-none transition-all duration-200', settings.fontFamily)}
                    style={{
                        fontSize: `${settings.fontSize}px`,
                        lineHeight: settings.lineHeight,
                    }}
                >
                    <div
                        className="whitespace-pre-wrap"
                        dangerouslySetInnerHTML={{
                            __html: SAMPLE_ARTICLE.replace(/\n/g, '<br />')
                                .replace(/# (.*?)<br \/>/g, '<h1 class="text-balance">$1</h1>')
                                .replace(/## (.*?)<br \/>/g, '<h2 class="text-balance">$1</h2>')
                                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>'),
                        }}
                    />
                </article>
            </div>
        </div>
    );
}

const SAMPLE_ARTICLE = `
# The Future of Reading: How Technology Transforms Text

In an age where information flows faster than ever before, the way we consume written content continues to evolve. Digital reading platforms have revolutionized not just how we access books and articles, but how we interact with text itself.

## The Evolution of Digital Typography

Typography in the digital age has become more than just choosing a font. It's about creating an experience that adapts to individual preferences and reading contexts. Modern reading applications understand that one size doesn't fit all—some readers prefer the classic elegance of serif fonts, while others find sans-serif typefaces easier on the eyes during extended reading sessions.

The ability to adjust font size, line spacing, and column width isn't just about comfort—it's about accessibility. These features ensure that reading remains inclusive, accommodating various visual needs and preferences.

## AI-Powered Content Curation

Artificial intelligence is beginning to play a significant role in how we process written content. Smart extraction algorithms can identify the core message of an article, removing distractions like advertisements and navigation elements to present clean, focused text.

This technology doesn't just clean up the visual presentation—it can also summarize key points, highlight important passages, and even suggest related content based on reading patterns and preferences.

## The Psychology of Reading Environments

Research shows that our reading environment significantly impacts comprehension and retention. The color temperature of our screens, the contrast between text and background, and even the width of text columns all influence how effectively we process information.

Modern reading applications recognize this by offering theme options that go beyond simple dark and light modes. Warm, paper-like backgrounds can reduce eye strain during long reading sessions, while high-contrast themes ensure text remains legible in various lighting conditions.

## Looking Forward

As we continue to digitize our reading experiences, the focus shifts from simply displaying text to creating personalized, adaptive reading environments. The future of reading lies not in replacing traditional books, but in enhancing how we interact with written content across all mediums.

The tools we build today will shape how future generations consume, understand, and engage with the written word. By prioritizing accessibility, personalization, and intelligent content curation, we're not just changing how we read—we're improving how we learn and think.
`;
