// 扩展消息类型
export interface ExtensionMessage {
    type: string;
    data?: any;
}

// 页面信息类型
export interface PageInfo {
    url: string;
    title: string;
    timestamp: number;
}

// 用户数据类型
export interface UserData {
    name: string;
    email: string;
    avatar?: string;
}

// 扩展设置类型
export interface ExtensionSettings {
    autoOpen: boolean;
    notifications: boolean;
    theme: 'light' | 'dark';
    language: string;
}

// 扩展状态类型
export interface ExtensionState {
    isActive: boolean;
    isVisible: boolean;
    currentTab?: chrome.tabs.Tab;
}

// Chrome API 类型扩展
declare global {
    interface Window {
        chrome: typeof chrome;
    }
}

export { };
