// dom-observer-ts.ts
interface ObserverOptions {
  container?: Element;
  existing?: boolean;
  once?: boolean;
  childList?: boolean;
  subtree?: boolean;
  attributes?: boolean;
  attributeFilter?: string[];
}

interface ElementCallback<T extends Element = Element> {
  (element: T, mutation?: MutationRecord): void;
}

export class DOMElementObserver<T extends Element = Element> {
  private observer: MutationObserver;
  private selector: string;
  private callback: ElementCallback<T>;
  private options: ObserverOptions;
  private isDestroyed = false;
  
  constructor(
    selector: string,
    callback: ElementCallback<T>,
    options: ObserverOptions = {}
  ) {
    this.selector = selector;
    this.callback = callback;
    this.options = {
      container: document.body,
      existing: false,
      once: false,
      childList: true,
      subtree: true,
      attributes: false,
      ...options
    };
    
    this.observer = new MutationObserver(this.handleMutations.bind(this));
    this.start();
  }
  
  private start(): void {
    const { container, existing } = this.options;
    
    // 处理已存在的元素
    if (existing) {
      const existingElements = container!.querySelectorAll<T>(this.selector);
      existingElements.forEach((element) => {
        this.callback(element);
        if (this.options.once) {
          this.destroy();
          return;
        }
      });
    }
    
    // 开始监听
    if (!this.isDestroyed) {
      this.observer.observe(container!, {
        childList: this.options.childList,
        subtree: this.options.subtree,
        attributes: this.options.attributes,
        attributeFilter: this.options.attributeFilter
      });
    }
  }
  
  private handleMutations(mutations: MutationRecord[]): void {
    if (this.isDestroyed) return;
    
    for (const mutation of mutations) {
      if (mutation.type === 'childList' && mutation.addedNodes.length) {
        this.handleAddedNodes(Array.from(mutation.addedNodes), mutation);
      } else if (mutation.type === 'attributes') {
        const target = mutation.target as Element;
        if (target.matches(this.selector)) {
          this.callback(target as T, mutation);
          if (this.options.once) {
            this.destroy();
            return;
          }
        }
      }
    }
  }
  
  private handleAddedNodes(nodes: Node[], mutation: MutationRecord): void {
    for (const node of nodes) {
      if (node.nodeType === Node.ELEMENT_NODE) {
        const element = node as Element;
        
        // 检查元素本身
        if (element.matches(this.selector)) {
          this.callback(element as T, mutation);
          if (this.options.once) {
            this.destroy();
            return;
          }
        }
        
        // 检查子元素
        const childElements = element.querySelectorAll<T>(this.selector);
        for (const child of childElements) {
          this.callback(child, mutation);
          if (this.options.once) {
            this.destroy();
            return;
          }
        }
      }
    }
  }
  
  destroy(): void {
    if (!this.isDestroyed) {
      this.observer.disconnect();
      this.isDestroyed = true;
    }
  }
  
  get destroyed(): boolean {
    return this.isDestroyed;
  }
}

// 便捷函数
export const observeElements = <T extends Element = Element>(
  selector: string,
  callback: ElementCallback<T>,
  options?: ObserverOptions
): DOMElementObserver<T> => {
  return new DOMElementObserver(selector, callback, options);
};

export const waitForElement = <T extends Element = Element>(
  selector: string,
  options?: Omit<ObserverOptions, 'once'>
): Promise<T> => {
  return new Promise((resolve) => {
    observeElements<T>(selector, (element) => {
      resolve(element);
    }, { ...options, once: true });
  });
};