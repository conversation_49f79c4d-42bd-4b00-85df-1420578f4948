# Chrome扩展加载指南

## 问题解决

Chrome浏览器成功启动，但扩展没有显示的原因是：**Chrome扩展需要手动在扩展管理页面加载和启用**。

## 解决步骤

### 1. 打开Chrome扩展管理页面
Chrome启动后会自动打开 `chrome://extensions/` 页面。如果没有自动打开，请手动访问：
```
chrome://extensions/
```

### 2. 启用开发者模式
在扩展管理页面的右上角，找到"开发者模式"开关并**开启它**。

### 3. 加载扩展
1. 点击"加载已解压的扩展程序"按钮
2. 选择扩展目录：`/Users/<USER>/Desktop/project/browser-plugin/chrome-extension-vite/dist`
3. 点击"选择文件夹"

### 4. 验证扩展加载
加载成功后，你应该能看到：
- 扩展名称：Chrome Extension Vite
- 版本：1.0.0
- 状态：已启用

### 5. 测试扩展功能
1. 在Chrome工具栏中查找扩展图标
2. 点击扩展图标测试功能
3. 访问任意网页测试content script

## 开发流程

### 修改代码后
1. 运行 `npm run build` 重新构建扩展
2. 在扩展管理页面点击扩展的"刷新"按钮
3. 测试新功能

### 调试扩展
1. 在扩展管理页面点击"检查视图"或"service worker"
2. 使用Chrome开发者工具调试
3. 查看Console输出和错误信息

## 常见问题

### 扩展没有显示在工具栏
- 检查扩展是否已启用
- 点击Chrome工具栏的扩展图标（拼图形状）
- 确保扩展已固定到工具栏

### 扩展加载失败
- 检查 `dist/manifest.json` 文件是否存在
- 确保所有依赖文件都已构建
- 查看Chrome扩展管理页面的错误信息

### 扩展功能不工作
- 检查Console是否有JavaScript错误
- 确保权限设置正确
- 验证content script是否在目标页面加载

## 文件结构说明

```
dist/
├── manifest.json      # 扩展配置文件
├── background.js      # 后台脚本
├── content.js         # 内容脚本
├── iframe.js          # iframe脚本
├── content-iframe.html        # iframe页面
└── iframe.css         # iframe样式
```

## 扩展功能

根据manifest.json配置，此扩展包含：
- **Background Script**: 后台服务工作者
- **Content Script**: 在所有网页中注入的脚本
- **Action**: 扩展图标点击功能
- **Web Accessible Resources**: iframe.html资源

## 开发提示

1. **使用简化启动**：`npm run start:simple`（推荐）
2. **完整开发环境**：`npm run start`（包含Vite服务器）
3. **仅开发模式**：`npm run dev:extension`（文件监听）

## 下一步

现在你的Chrome扩展开发环境已经准备就绪！你可以：
1. 修改 `src/` 目录下的代码
2. 运行 `npm run build` 重新构建
3. 在Chrome中重新加载扩展
4. 测试新功能
