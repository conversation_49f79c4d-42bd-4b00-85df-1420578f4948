# Chrome扩展使用说明

## 项目概述

这是一个使用现代技术栈构建的Chrome扩展项目，完全符合您的要求：

- ✅ 支持Chrome扩展Manifest V3
- ✅ 技术栈：TypeScript + TailwindCSS + shadcn + lucide-react + zustand + zod + react + cash-dom
- ✅ 无popup.html，通过background脚本监听action点击
- ✅ Content script创建iframe并渲染React应用
- ✅ 支持热更新开发
- ✅ 自动构建和启动脚本

## 快速开始

### 1. 安装依赖

```bash
npm install
```

### 2. 开发模式（推荐）

```bash
npm run dev:extension
```

这个命令会：
- 构建扩展
- 启动文件监听器
- 代码变化时自动重新构建
- 提供详细的使用说明

### 3. 测试构建

```bash
npm run test:build
```

### 4. 生产构建

```bash
npm run build
```

## 手动加载扩展

1. 打开Chrome浏览器
2. 访问 `chrome://extensions/`
3. 开启右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择项目的 `dist` 目录
6. 扩展加载完成后，访问任意网页
7. 点击扩展图标测试功能

## 功能说明

### 扩展架构

1. **Background Script** (`src/background.ts`)
   - 监听扩展图标点击事件
   - 向content script发送消息

2. **Content Script** (`src/content.ts`)
   - 接收background消息
   - 创建iframe并注入到页面
   - 管理iframe的显示/隐藏

3. **React应用** (`src/iframe.tsx`)
   - 在iframe中渲染的React应用
   - 使用现代UI组件和状态管理

### 主要功能

- 🎨 深色/浅色主题切换
- ⏰ 实时时间显示
- 📄 当前页面信息显示
- ⚙️ 扩展设置管理
- 🔄 状态持久化
- 📱 响应式设计

## 开发说明

### 文件结构

```
src/
├── background.ts          # Background脚本
├── content.ts           # Content脚本
├── iframe.tsx           # React应用入口
├── App.tsx              # 主应用组件
├── components/          # React组件
│   ├── Header.tsx      # 头部组件
│   ├── MainContent.tsx # 主内容组件
│   └── Footer.tsx      # 底部组件
├── stores/              # Zustand状态管理
│   └── extensionStore.ts
├── types/               # TypeScript类型
│   └── index.ts
└── utils/               # 工具函数
```

### 热更新开发

1. 运行 `npm run dev:extension`
2. 修改 `src/` 目录下的任何文件
3. 扩展会自动重新构建
4. 在Chrome中点击扩展的刷新按钮重新加载

### 调试

- **Background Script**: 在 `chrome://extensions/` 页面点击扩展的"检查视图"
- **Content Script**: 在网页的开发者工具中查看
- **React应用**: 在iframe的开发者工具中查看

## 自定义配置

### 修改目标网页

编辑 `scripts/start.js` 中的 `TARGET_URLS` 数组：

```javascript
const TARGET_URLS = [
  'https://www.google.com',
  'https://www.github.com',
  // 添加你的目标网页
];
```

### 添加新功能

1. 在 `src/components/` 中创建新组件
2. 在 `src/stores/extensionStore.ts` 中添加状态
3. 在 `src/types/index.ts` 中定义类型

### 修改样式

项目使用TailwindCSS，可以直接在组件中使用Tailwind类名，或者修改 `src/index.css` 文件。

## 部署

1. 运行 `npm run build`
2. 将 `dist` 目录打包为zip文件
3. 在Chrome扩展管理页面加载解压的扩展

## 故障排除

### 扩展无法加载

1. 检查 `dist/manifest.json` 是否存在
2. 确保所有构建文件都在 `dist` 目录中
3. 查看Chrome扩展页面的错误信息

### 热更新不工作

1. 确保 `npm run dev:extension` 正在运行
2. 检查文件监听器是否正常工作
3. 手动重新构建：`npm run build`

### Chrome浏览器未找到

1. 手动打开Chrome浏览器
2. 访问 `chrome://extensions/`
3. 手动加载扩展

## 技术栈说明

- **Vite**: 快速构建工具，支持热更新
- **React 18**: 现代React框架
- **TypeScript**: 类型安全的JavaScript
- **TailwindCSS**: 实用优先的CSS框架
- **Zustand**: 轻量级状态管理
- **Zod**: 数据验证库
- **Lucide React**: 现代图标库
- **Cash DOM**: 轻量级DOM操作库

## 许可证

MIT License
