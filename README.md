# Chrome Extension with Vite

一个使用现代技术栈构建的Chrome扩展项目，支持Manifest V3。

## 技术栈

- **构建工具**: Vite
- **前端框架**: React 18 + TypeScript
- **样式**: TailwindCSS
- **UI组件**: shadcn/ui + Lucide React
- **状态管理**: Zustand
- **数据验证**: Zod
- **DOM操作**: Cash DOM
- **Chrome API**: Manifest V3

## 功能特性

- ✅ 支持Chrome扩展Manifest V3
- ✅ 无popup.html，通过background脚本监听action点击
- ✅ Content script创建iframe并渲染React应用
- ✅ 热更新开发环境
- ✅ 自动打开Chrome浏览器并加载扩展
- ✅ 现代化UI设计
- ✅ 深色/浅色主题切换
- ✅ 状态持久化

## 项目结构

```
chrome-extension-vite/
├── public/
│   ├── manifest.json          # Chrome扩展清单文件
│   └── content-iframe.html           # iframe页面模板
├── src/
│   ├── background.ts         # Background脚本
│   ├── content.ts           # Content脚本
│   ├── iframe.tsx           # React应用入口
│   ├── App.tsx              # 主应用组件
│   ├── components/          # React组件
│   ├── stores/              # Zustand状态管理
│   ├── types/               # TypeScript类型定义
│   └── utils/               # 工具函数
├── scripts/
│   ├── start.js             # 启动脚本
│   └── hot-reload.js        # 热更新脚本
└── dist/                    # 构建输出目录
```

## 快速开始

### 1. 安装依赖

```bash
npm install
```

### 2. 开发模式

```bash
npm run dev
```

### 3. 启动完整开发环境

```bash
npm start
```

这个命令会：
- 构建扩展
- 启动开发服务器
- 自动打开Chrome浏览器
- 加载扩展
- 打开预设的网页

### 4. 构建生产版本

```bash
npm run build
```

## 开发说明

### 扩展架构

1. **Background Script**: 监听扩展图标点击，发送消息给content script
2. **Content Script**: 接收消息，创建iframe并注入到页面
3. **React App**: 在iframe中渲染的React应用

### 热更新

项目支持热更新，修改代码后会自动重新构建扩展。在Chrome中需要手动重新加载扩展：

1. 打开 `chrome://extensions/`
2. 找到你的扩展
3. 点击刷新按钮

### 调试

- **Background Script**: 在Chrome扩展管理页面点击"检查视图"
- **Content Script**: 在网页开发者工具中查看
- **React App**: 在iframe的开发者工具中查看

## 自定义配置

### 修改目标网页

编辑 `scripts/start.js` 中的 `TARGET_URLS` 数组：

```javascript
const TARGET_URLS = [
  'https://www.google.com',
  'https://www.github.com',
  // 添加你的目标网页
];
```

### 添加新功能

1. 在 `src/components/` 中创建新组件
2. 在 `src/stores/` 中添加状态管理
3. 在 `src/types/` 中定义类型

## 部署

1. 运行 `npm run build`
2. 将 `dist` 目录打包为zip文件
3. 在Chrome扩展管理页面加载解压的扩展

## 许可证

MIT License
