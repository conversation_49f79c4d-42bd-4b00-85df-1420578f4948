import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path, { resolve } from 'path';
import tailwindcss from '@tailwindcss/vite';

export default defineConfig({
    plugins: [react(), tailwindcss()],
    build: {
        outDir: 'dist',
        rollupOptions: {
            input: {
                background: resolve(__dirname, 'src/background.ts'),
                content: resolve(__dirname, 'src/content.ts'),
                iframe: resolve(__dirname, 'src/iframe.tsx'),
                tailwind: resolve(__dirname, 'src/tailwind.css'),
            },
            output: {
                entryFileNames: (chunkInfo) => {
                    if (chunkInfo.name === 'tailwind') {
                        return 'tailwind.css';
                    }
                    return '[name].js';
                },
                chunkFileNames: '[name].js',
                assetFileNames: '[name].[ext]',
            },
        },
        watch: {
            include: 'src/**',
        },
    },
    server: {
        port: 3000,
        strictPort: false,
        host: true,
        hmr: {
            port: 3001,
        },
    },
    define: {
        'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'development'),
    },
    resolve: {
        alias: {
            '@': path.resolve(__dirname, './src'),
        },
    },
});
